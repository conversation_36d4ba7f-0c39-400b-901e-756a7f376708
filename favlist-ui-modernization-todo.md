# FavList 收藏页面 UI 现代化改造 - 待办任务清单

## 📋 项目概况

**目标**：对 `YaoHuo.Plugin` 项目中的 `Favlist.aspx` 页面进行 UI 现代化改造，使用 Handlebars.NET 替换旧的 ASP.NET Web Forms UI，并实现新旧 UI 可切换。

**相关文件路径**：

- 后端代码：`YaoHuo.Plugin/BBS/Favlist.aspx.cs`
- 旧版前端：`YaoHuo.Plugin/BBS/Favlist.aspx`
- 参考设计：`YaoHuo.Plugin/Template/Pages/FriendList.hbs`（好友列表UI布局参考）
- 参考模型：`YaoHuo.Plugin/Template/Models/FriendListPageModel.cs`（数据模型参考）
- 模板服务：`YaoHuo.Plugin/WebSite/Tool/TemplateService.cs`
- 头部模型：`YaoHuo.Plugin/Template/Models/HeaderOptionsModel.cs`
- 主布局：`YaoHuo.Plugin/Template/Layouts/MainLayout.hbs`
- 安全查询服务：`YaoHuo.Plugin/WebSite/Services/FriendQueryService.cs`（参考安全实现）

## 🎯 核心任务分解

### 阶段一：安全问题修复与查询服务创建

#### ✅ 任务 1.1：创建 FavQueryService 安全查询服务

- [x] **文件路径**：`YaoHuo.Plugin/WebSite/Services/FavQueryService.cs`
- [x] **任务详情**：
  - [x] 参考 `FriendQueryService.cs` 的实现模式
  - [x] 实现参数化查询防止 SQL 注入
  - [x] 支持按收藏标题的智能搜索功能
  - [x] 支持分页查询
  - [x] 定义 `FavQueryResult` 结果类
- [x] **核心方法**：
  - [x] `SearchFavorites()` - 搜索收藏列表
  - [x] `GetFavoriteCount()` - 获取收藏数量
  - [x] `FavoriteExists()` - 验证收藏是否存在

#### ✅ 任务 1.2：修复现有 SQL 注入安全问题

- [x] **文件路径**：`YaoHuo.Plugin/BBS/Favlist.aspx.cs` 中的 `showclass` 方法
- [x] **任务详情**：
  - [x] 找到第92行的搜索条件：`condition = condition + " and title like '%" + key + "%'"`
  - [x] 将字符串拼接改为使用 `FavQueryService` 的参数化查询
  - [x] 确保所有数据库操作都通过安全的查询服务进行
- [x] **注意事项**：
  - [x] 保持与原有查询逻辑的兼容性
  - [x] 确保分页功能正常工作

### 阶段二：数据模型设计与创建

#### ✅ 任务 2.1：创建 FavList 页面数据模型

- [x] **文件路径**：`YaoHuo.Plugin/Template/Models/FavListPageModel.cs`
- [x] **任务详情**：
  - [x] 参考 `FriendListPageModel.cs` 的结构设计
  - [x] 定义主要页面模型 `FavListPageModel` 类
  - [x] 包含页面标题、消息状态、搜索数据、收藏列表数据、分页信息等属性
  - [x] 定义子模型：
    - [x] `MessageModel`：处理 INFO/ERROR 消息显示（复用现有）
    - [x] `SearchModel`：搜索框相关数据（集成到主模型）
    - [x] `FavItemModel`：单个收藏项的数据结构
    - [x] `PaginationModel`：分页导航数据（复用现有）
- [x] **特殊考虑**：
  - [x] 根据 `favtypeid` 值设计条件渲染的数据结构
  - [x] 支持收藏分类显示（当 `favtypeid != "0"` 时显示分类名称）

#### ✅ 任务 2.2：验证数据模型完整性

- [x] **检查项**：
  - [x] 确保所有收藏类型的数据需求都被覆盖
  - [x] 验证模型与后端 `listVo` 数据的映射关系
  - [x] 确认与 `favsubject_Model` 的集成

### 阶段三：Handlebars 模板创建

#### ✅ 任务 3.1：创建 FavList Handlebars 模板

- [x] **文件路径**：`YaoHuo.Plugin/Template/Pages/FavList.hbs`
- [x] **任务详情**：
  - [x] **参考设计文件**：
    - [x] 基于 `FriendList.hbs` 的布局结构进行适配
    - [x] 调整为收藏列表的展示方式
  - [x] **模板结构**：
    - [x] 渲染页面头部（根据 favtypeid 动态显示标题）
    - [x] 渲染搜索框（支持按标题搜索）
    - [x] 渲染收藏列表（使用 `{{#each}}` 循环）
    - [x] 渲染分页导航
    - [x] 渲染操作按钮（删除收藏、清空收藏等）
- [x] **功能要求**：
  - [x] 实现收藏链接的正确跳转（内部链接和外部链接）
  - [x] 添加前端确认对话框的 JavaScript 代码
  - [x] 实现分页 HTML 解析和重新生成的 JavaScript 逻辑
  - [x] 显示收藏时间信息

#### ✅ 任务 3.2：适配收藏列表特有的UI元素

- [x] **任务详情**：
  - [x] 设计收藏项的图标（书签、链接等）
  - [x] 适配收藏链接的显示方式
  - [x] 处理内部链接和外部链接的不同展示
  - [x] 添加收藏时间的友好显示
  - [x] 修复UI布局：每条收藏改为独立卡片布局
  - [x] 调整三点按钮位置：右边垂直居中
  - [x] 更新JavaScript选择器以匹配新的HTML结构
  - [x] 移除不必要的头像元素（收藏页面不需要头像）
  - [x] 优化点击区域：只有标题可点击，防止误触
  - [x] 简化操作：三点菜单改为垃圾桶图标，移除"访问"选项
  - [x] 移除链接类型标签：不再显示"内部链接"和"外部链接"
  - [x] 垃圾桶图标样式：参考好友页面，灰色默认，hover变红色
  - [x] 优化空状态体验：没有收藏内容时隐藏搜索框，界面更简洁

### 阶段四：后端代码改造

#### ✅ 任务 4.1：实现新旧 UI 切换逻辑

#### ✅ 任务 4.2：整合删除功能并实现Ajax操作

- [x] **任务详情**：
  - [x] 将 `Favlist_del.aspx.cs` 的删除功能整合到主页面 `Favlist.aspx.cs`
  - [x] 添加 `HandleDeleteFavorite()` 方法处理单个收藏删除
  - [x] 添加 `HandleDeleteAllFavorites()` 方法处理清空收藏
  - [x] 实现 `WriteJsonResponse()` 方法输出JSON响应
  - [x] 更新删除URL指向主页面而非独立删除页面
  - [x] 实现Ajax删除操作，提升用户体验
  - [x] 添加删除动画效果（淡出+滑动）
  - [x] 实现Ajax清空收藏功能
  - [x] 添加Toast通知系统显示操作结果
  - [x] 添加加载状态指示器（旋转图标）
  - [x] 完善错误处理和网络异常处理
  - [x] 修复类型转换错误：`MainBll.UpdateSQL` 返回 `long` 改为 `int`
  - [x] 安全改进：使用参数化查询替代字符串拼接，防止SQL注入
  - [x] 添加必要的 using 引用：`KeLin.ClassManager.ExUtility`、`System.Data`、`System.Data.SqlClient`

- [x] **文件路径**：`YaoHuo.Plugin/BBS/Favlist.aspx.cs`
- [x] **任务详情**：
  - [x] 参考 `FriendList.aspx.cs` 的实现模式
  - [x] 在 `Page_Load` 方法开头添加 UI 偏好检查逻辑
  - [x] 实现 `CheckAndHandleUIPreference()` 方法
  - [x] 实现 `TryRenderWithHandlebars()` 方法
  - [x] 确保正确处理 `ThreadAbortException`
  - [x] 当新版渲染成功时阻止旧版代码执行

#### ✅ 任务 4.2：重构数据加载逻辑使用安全查询服务

- [x] **文件路径**：`YaoHuo.Plugin/BBS/Favlist.aspx.cs`
- [x] **任务详情**：
  - [x] 修改 `showclass` 方法使用 `FavQueryService`
  - [x] 实现 `LoadDataForNewUI()` 私有方法
  - [x] 处理不同 `favtypeid` 的查询逻辑
  - [x] 构建消息状态（INFO/ERROR）的显示逻辑
  - [x] 处理搜索功能的数据绑定

#### ✅ 任务 4.3：实现数据模型构建逻辑

- [x] **文件路径**：`YaoHuo.Plugin/BBS/Favlist.aspx.cs`
- [x] **任务详情**：
  - [x] 实现 `BuildFavListPageModel()` 私有方法
  - [x] 处理不同 `favtypeid` 的页面标题和内容差异
  - [x] 构建收藏列表数据
  - [x] 处理收藏分类信息（当 `favtypeid != "0"` 时）
  - [x] 构建分页信息

#### ✅ 任务 4.4：实现 Handlebars 渲染调用

- [x] **文件路径**：`YaoHuo.Plugin/BBS/Favlist.aspx.cs`
- [x] **任务详情**：
  - [x] 实现 `RenderWithHandlebars()` 方法
  - [x] 调用 `TemplateService.RenderPageWithLayout` 方法
  - [x] 配置正确的模板路径：`~/Template/Pages/FavList.hbs`
  - [x] 根据 `favtypeid` 动态设置页面标题
  - [x] 配置适当的 `HeaderOptionsModel`
  - [x] 处理渲染错误和异常情况

### 阶段五：前端功能实现

#### ✅ 任务 5.1：实现分页 HTML 解析功能

- [x] **位置**：`FavList.hbs` 模板中的 JavaScript 代码
- [x] **任务详情**：
  - [x] 参考 `FriendList.hbs` 的分页实现
  - [x] 解析后端返回的 `linkURL` HTML 字符串
  - [x] 提取分页链接、当前页、总页数、总条数等信息
  - [x] 重新生成符合新版 UI 样式的分页导航
  - [x] 确保分页链接点击后的跳转功能正常

#### ✅ 任务 5.2：实现前端确认对话框

- [x] **位置**：`FavList.hbs` 模板中的 JavaScript 代码
- [x] **任务详情**：
  - [x] 为删除收藏操作添加确认提示
  - [x] 为清空收藏操作添加确认提示
  - [x] 参考 `FriendList.hbs` 的自定义确认对话框实现
  - [x] 确保用户体验友好

#### ✅ 任务 5.3：实现搜索功能

- [x] **位置**：`FavList.hbs` 模板
- [x] **任务详情**：
  - [x] 渲染搜索表单
  - [x] 绑定搜索关键字 `key` 变量
  - [x] 确保搜索表单提交到正确的后端接口
  - [x] 支持按收藏标题搜索

### 阶段六：测试与验证

#### ✅ 任务 6.1：功能测试

- [ ] **测试项目**：
  - [ ] 测试不同 `favtypeid` 值下的页面显示
    - [ ] `favtypeid=0`：全部收藏 + 搜索功能
    - [ ] `favtypeid!=0`：特定分类收藏
  - [ ] 测试新旧 UI 切换功能
  - [ ] 测试搜索功能（按标题搜索）
  - [ ] 测试分页导航功能
  - [ ] 测试删除收藏的确认提示
  - [ ] 测试清空收藏功能

#### ✅ 任务 6.2：安全测试

- [ ] **测试项目**：
  - [ ] 验证 `FavQueryService` 中的 SQL 注入修复
  - [ ] 测试参数化查询是否正确执行
  - [ ] 验证用户输入的安全过滤
  - [ ] 测试搜索功能的安全性

#### ✅ 任务 6.3：样式测试

- [ ] **测试项目**：
  - [ ] 检查新版 UI 是否有样式问题
  - [ ] 测试新旧样式是否存在冲突
  - [ ] 验证响应式设计在不同设备上的显示效果
  - [ ] 确认收藏链接的视觉表现

#### ✅ 任务 6.4：错误处理测试

- [ ] **测试项目**：
  - [ ] 测试数据模型构建中的异常处理
  - [ ] 测试模板渲染失败时的回退机制
  - [ ] 验证错误消息的显示是否正确

### 阶段七：代码质量与优化

#### ✅ 任务 7.1：代码审查

- [ ] **审查项目**：
  - [ ] 确保代码符合项目的 C# 编码规范
  - [ ] 验证命名约定的一致性
  - [ ] 检查注释和文档的完整性
  - [ ] 确认错误处理的完备性

#### ✅ 任务 7.2：性能优化

- [ ] **优化项目**：
  - [ ] 确认模板缓存机制工作正常
  - [ ] 检查数据模型构建的效率
  - [ ] 验证前端 JavaScript 的性能
  - [ ] 优化查询服务的性能

#### ✅ 任务 7.3：项目文件管理

- [x] **文件操作**：
  - [x] 确保新创建的文件已添加到 `YaoHuo.Plugin.csproj` 项目中
  - [x] 验证文件的"生成操作"设置正确
  - [x] 检查命名空间引用的正确性

## 🎯 成功标准检查清单

- [ ] FavList 页面能够成功通过 Handlebars 渲染出新的 UI
- [ ] 新 UI 在不同 `favtypeid` 下能正确显示相应的数据
- [ ] 新旧 UI 切换功能工作正常
- [ ] 分页导航通过前端 JS 成功重新生成并可交互
- [ ] 删除等操作的前端确认提示工作正常
- [ ] 没有明显的 UI 错误或样式问题
- [ ] `FavQueryService` 使用参数化查询，消除了 SQL 注入风险
- [ ] 搜索功能在新版 UI 下工作正常
- [ ] 收藏链接（内部和外部）正确跳转
- [ ] 错误处理机制完善，提供良好的用户体验

## 📝 注意事项

1. **数据结构差异**：收藏表 `favdetail` 与好友表 `wap_friends` 结构不同，需要适配
2. **链接处理**：收藏包含内部链接和外部链接，需要正确处理跳转
3. **安全优先**：必须修复 SQL 注入问题，使用参数化查询
4. **兼容性保证**：确保新旧 UI 切换机制的稳定性
5. **样式一致性**：新版 UI 应与项目整体设计风格保持一致
6. **分类支持**：支持收藏分类的显示和管理

## 🔄 依赖关系

- 任务 1（安全修复和查询服务）是所有其他任务的前置条件
- 任务 2（数据模型）依赖任务 1 的完成
- 任务 3（模板创建）依赖任务 2 的完成
- 任务 4（后端改造）依赖任务 1、2 的完成
- 任务 5（前端功能）依赖任务 3 的完成
- 任务 6（测试）依赖前面所有开发任务的完成

---

**创建时间**：2024年12月19日
**项目版本**：基于 .NET Framework 4.8 + Handlebars.NET
**参考文档**：`handlebars-integration-reference.md`、`friendlist-ui-modernization-todo.md`
