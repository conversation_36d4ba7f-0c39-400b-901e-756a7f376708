{{! 状态消息显示 }}
{{#if Message.HasMessage}}
<div class="message {{Message.Type}} mb-4">
    {{#if Message.IsSuccess}}
    <div class="card text-center border border-border-light">
        <div class="bg-white text-text-primary py-8 px-4 pb-6 border-b border-border-light">
            <div class="mb-4">
                <i data-lucide="check-circle" class="w-16 h-16 stroke-2 text-primary mx-auto"></i>
            </div>
            <h3 class="text-xl font-semibold m-0 text-text-primary">兑换成功</h3>
        </div>
        <div class="py-6 px-4 pb-8 bg-white">
            <p class="text-text-secondary mb-4 leading-6">{{Message.Content}}</p>
            <div class="flex justify-center items-center">
                <a href="{{FormData.HiddenFields.BackUrl}}" class="btn btn-primary py-3 px-6 text-base">
                    <i data-lucide="arrow-left" class="mr-2"></i>
                    返回
                </a>
            </div>
        </div>
    </div>
    {{else}}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
            <i data-lucide="alert-circle" class="w-5 h-5 text-red-500 mt-0.5 mr-3"></i>
            <div class="text-red-800">{{Message.Content}}</div>
        </div>
    </div>
    {{/if}}
</div>
{{/if}}

{{! 只有在非成功状态时才显示主要内容 }}
{{#unless Message.IsSuccess}}

{{! 账户信息卡片 }}
<div class="card mb-4">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="wallet" class="card-icon"></i>
            我的资产
        </h2>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-gray-600 text-sm mb-1">我的 RMB</div>
                <div class="text-xl font-medium text-primary">{{UserAssets.RMBDisplay}}</div>
            </div>
            <div class="text-center">
                <div class="text-gray-600 text-sm mb-1">我的{{UserAssets.MoneyName}}</div>
                <div class="text-xl font-medium text-primary">{{UserAssets.MoneyDisplay}}</div>
            </div>
        </div>
    </div>
</div>

{{! 切换标签 }}
<div class="bg-gray-100 rounded-full p-1 flex mb-4">
    <button id="exchangeTab" class="flex-1 py-2 px-4 rounded-full bg-primary text-white text-center transition-all">兑换</button>
    <button id="rechargeTab" class="flex-1 py-2 px-4 rounded-full text-gray-700 text-center transition-all">充值</button>
</div>

{{! 兑换模块 }}
<div id="exchangeSection" class="card mb-4">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="repeat" class="card-icon"></i>
            兑换{{UserAssets.MoneyName}}
        </h2>
    </div>
    <div class="card-body">
        {{#if ExchangeSettings.IsEnabled}}
        <div class="bg-green-50 rounded-lg p-3 mb-4">
            <div class="flex items-center gap-2">
                <i data-lucide="exchange-line" class="text-green-500"></i>
                <p class="text-green-800 font-medium m-0">兑换比例：{{ExchangeSettings.RateDisplay}}</p>
            </div>
        </div>

        <form id="exchange-form" action="{{FormData.ActionUrl}}" method="post">
            <div class="form-group">
                <label class="form-label required">输入金额</label>
                <div class="relative">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">¥</span>
                    <input type="number" 
                           id="exchangeAmount" 
                           name="tomoney"
                           class="form-input pl-8" 
                           placeholder="请输入RMB数字"
                           value="{{FormData.ExchangeAmount}}"
                           step="0.01"
                           min="0.01"
                           max="{{ExchangeSettings.MaxExchangeAmount}}"
                           required>
                </div>
                <div class="mt-2 bg-blue-50 rounded-lg p-3">
                    <div class="flex items-center gap-2">
                        <i data-lucide="coins" class="text-blue-500"></i>
                        <span class="text-gray-700">可兑换</span>
                        <span id="crystalAmount" class="text-primary font-medium">0</span>
                        <span class="text-gray-700">{{UserAssets.MoneyName}}</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label required">我的密码</label>
                <input type="password" 
                       id="password" 
                       name="changePW"
                       class="form-input" 
                       placeholder="请输入密码确认兑换"
                       autocomplete="current-password"
                       required>
            </div>

            {{! 隐藏字段 }}
            <input type="hidden" name="action" value="{{FormData.HiddenFields.Action}}">
            <input type="hidden" name="siteid" value="{{FormData.HiddenFields.SiteId}}">
            <input type="hidden" name="backurl" value="{{FormData.HiddenFields.BackUrl}}">

            <button type="submit" id="exchangeBtn" class="btn btn-primary w-full py-3">
                <i data-lucide="repeat" class="w-5 h-5 mr-2"></i>
                确认兑换
            </button>
        </form>
        {{else}}
        <div class="bg-red-50 rounded-lg p-4 text-center">
            <i data-lucide="x-circle" class="w-12 h-12 text-red-500 mx-auto mb-2"></i>
            <p class="text-red-800 font-medium">站长已关闭此功能</p>
        </div>
        {{/if}}
    </div>
</div>

{{! 充值模块 }}
<div id="rechargeSection" class="card mb-4 hidden">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="credit-card" class="card-icon"></i>
            扫码充值
        </h2>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-2 gap-4 mb-4">
            {{#if RechargeInfo.SupportsAlipay}}
            <button id="alipayBtn" class="flex items-center justify-center gap-2 py-3 px-4 bg-blue-50 text-blue-600 rounded-lg transition-all">
                <i data-lucide="smartphone" class="w-5 h-5"></i>
                <span>支付宝</span>
            </button>
            {{/if}}
            {{#if RechargeInfo.SupportsWechat}}
            <button id="wechatBtn" class="flex items-center justify-center gap-2 py-3 px-4 bg-gray-100 text-gray-400 rounded-lg transition-all">
                <i data-lucide="message-circle" class="w-5 h-5"></i>
                <span>微信支付</span>
            </button>
            {{/if}}
        </div>
        
        <button id="showQRCodeBtn" class="w-full flex items-center justify-center gap-2 py-3 px-4 bg-primary text-white rounded-lg mb-4">
            <i data-lucide="qr-code" class="w-5 h-5"></i>
            <span>显示付款二维码</span>
        </button>

        <div class="bg-blue-50 rounded-lg p-3 mb-4">
            <div class="flex items-start gap-2">
                <i data-lucide="info" class="text-blue-500 mt-0.5"></i>
                <div class="flex-1">
                    <p class="text-blue-800 font-medium m-0">付款时请"添加备注"你的ID</p>
                    <div class="flex items-center gap-2 mt-1">
                        <p class="text-blue-700 m-0">你的ID号为：<span id="userId" class="text-primary font-semibold">{{RechargeInfo.UserId}}</span></p>
                        <button id="copyIdBtn" class="flex items-center justify-center w-6 h-6 bg-white rounded-full shadow-sm hover:bg-gray-50 transition-colors">
                            <i data-lucide="copy" class="w-3 h-3 text-gray-500"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="border-t border-gray-100 pt-3">
            <p class="text-gray-600 text-sm m-0">{{RechargeInfo.Instructions}}</p>
        </div>
    </div>
</div>

{{/unless}}

{{! 二维码弹窗 }}
<div id="qrCodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg w-5/6 max-w-md">
        <div class="p-4 border-b border-gray-100 flex justify-between items-center">
            <h3 class="text-lg font-medium">付款二维码</h3>
            <button id="closeQRCodeBtn" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
            </button>
        </div>
        <div class="p-6 flex justify-center">
            <div class="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                <i data-lucide="qr-code" class="w-24 h-24 text-gray-400"></i>
            </div>
        </div>
        <div class="p-4 border-t border-gray-100 text-center">
            <p class="text-gray-600 text-sm m-0">请使用支付宝或微信扫码付款</p>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图标
        lucide.createIcons();

        // 标签切换功能
        setupTabSwitching();

        // 兑换计算功能
        setupExchangeCalculator();

        // 充值功能
        setupRechargeFeatures();

        // 表单验证和提交
        setupFormValidation();
    });

    // 标签切换功能
    function setupTabSwitching() {
        const rechargeTab = document.getElementById('rechargeTab');
        const exchangeTab = document.getElementById('exchangeTab');
        const rechargeSection = document.getElementById('rechargeSection');
        const exchangeSection = document.getElementById('exchangeSection');

        if (!rechargeTab || !exchangeTab || !rechargeSection || !exchangeSection) return;

        // 充值标签点击
        rechargeTab.addEventListener('click', function() {
            rechargeTab.classList.add('bg-primary', 'text-white');
            rechargeTab.classList.remove('text-gray-700');
            exchangeTab.classList.remove('bg-primary', 'text-white');
            exchangeTab.classList.add('text-gray-700');
            rechargeSection.classList.remove('hidden');
            exchangeSection.classList.add('hidden');
        });

        // 兑换标签点击
        exchangeTab.addEventListener('click', function() {
            exchangeTab.classList.add('bg-primary', 'text-white');
            exchangeTab.classList.remove('text-gray-700');
            rechargeTab.classList.remove('bg-primary', 'text-white');
            rechargeTab.classList.add('text-gray-700');
            exchangeSection.classList.remove('hidden');
            rechargeSection.classList.add('hidden');
        });
    }

    // 兑换计算功能
    function setupExchangeCalculator() {
        const exchangeAmount = document.getElementById('exchangeAmount');
        const crystalAmount = document.getElementById('crystalAmount');

        if (!exchangeAmount || !crystalAmount) return;

        // 从页面获取兑换比例
        const exchangeRate = {{ExchangeSettings.ExchangeRate}};

        exchangeAmount.addEventListener('input', function() {
            const amount = parseFloat(this.value) || 0;
            const crystals = Math.floor(amount * exchangeRate);
            crystalAmount.textContent = crystals.toLocaleString();
        });
    }

    // 充值功能
    function setupRechargeFeatures() {
        const alipayBtn = document.getElementById('alipayBtn');
        const wechatBtn = document.getElementById('wechatBtn');
        const showQRCodeBtn = document.getElementById('showQRCodeBtn');
        const qrCodeModal = document.getElementById('qrCodeModal');
        const closeQRCodeBtn = document.getElementById('closeQRCodeBtn');
        const copyIdBtn = document.getElementById('copyIdBtn');
        const userId = document.getElementById('userId');

        // 支付方式选择
        if (alipayBtn && wechatBtn) {
            alipayBtn.addEventListener('click', function() {
                alipayBtn.classList.remove('bg-gray-100', 'text-gray-400');
                alipayBtn.classList.add('bg-blue-50', 'text-blue-600');
                wechatBtn.classList.remove('bg-green-50', 'text-green-600');
                wechatBtn.classList.add('bg-gray-100', 'text-gray-400');
            });

            wechatBtn.addEventListener('click', function() {
                wechatBtn.classList.remove('bg-gray-100', 'text-gray-400');
                wechatBtn.classList.add('bg-green-50', 'text-green-600');
                alipayBtn.classList.remove('bg-blue-50', 'text-blue-600');
                alipayBtn.classList.add('bg-gray-100', 'text-gray-400');
            });
        }

        // 显示二维码弹窗
        if (showQRCodeBtn && qrCodeModal) {
            showQRCodeBtn.addEventListener('click', function() {
                qrCodeModal.classList.remove('hidden');
                lucide.createIcons();
            });
        }

        // 关闭二维码弹窗
        if (closeQRCodeBtn && qrCodeModal) {
            closeQRCodeBtn.addEventListener('click', function() {
                qrCodeModal.classList.add('hidden');
            });

            // 点击弹窗外部关闭
            qrCodeModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                }
            });
        }

        // 复制用户ID
        if (copyIdBtn && userId) {
            copyIdBtn.addEventListener('click', function() {
                const id = userId.textContent;
                navigator.clipboard.writeText(id).then(() => {
                    // 显示复制成功提示
                    showToast('复制成功');

                    // 临时改变图标
                    copyIdBtn.innerHTML = '<i data-lucide="check" class="w-3 h-3 text-green-500"></i>';
                    lucide.createIcons();

                    setTimeout(() => {
                        copyIdBtn.innerHTML = '<i data-lucide="copy" class="w-3 h-3 text-gray-500"></i>';
                        lucide.createIcons();
                    }, 1500);
                }).catch(() => {
                    showToast('复制失败，请手动复制');
                });
            });
        }
    }

    // 表单验证和提交
    function setupFormValidation() {
        const form = document.getElementById('exchange-form');
        const submitBtn = document.getElementById('exchangeBtn');
        const amountInput = document.getElementById('exchangeAmount');
        const passwordInput = document.getElementById('password');

        if (!form || !submitBtn) return;

        form.addEventListener('submit', function(e) {
            // 基础验证
            if (amountInput && !amountInput.value.trim()) {
                e.preventDefault();
                showFieldError(amountInput, '请输入兑换金额');
                return;
            }

            if (passwordInput && !passwordInput.value.trim()) {
                e.preventDefault();
                showFieldError(passwordInput, '请输入密码');
                return;
            }

            // 显示提交状态
            submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 animate-spin mr-2"></i>兑换中...';
            submitBtn.disabled = true;

            // 重新创建图标
            setTimeout(() => {
                lucide.createIcons();
            }, 10);
        });

        // 输入框验证
        if (amountInput) {
            amountInput.addEventListener('input', function() {
                clearFieldError(this);

                const value = parseFloat(this.value);
                const maxAmount = {{ExchangeSettings.MaxExchangeAmount}};

                if (value > maxAmount) {
                    showFieldError(this, `每次不能大于¥${maxAmount.toFixed(2)}`);
                }
            });
        }

        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                clearFieldError(this);
            });
        }
    }

    // 显示字段错误
    function showFieldError(field, message) {
        clearFieldError(field);
        field.classList.add('border-red-500');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-xs text-red-500 mt-1';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    // 清除字段错误
    function clearFieldError(field) {
        field.classList.remove('border-red-500');
        const existingError = field.parentNode.querySelector('.text-xs.text-red-500');
        if (existingError) {
            existingError.remove();
        }
    }

    // 显示Toast提示
    function showToast(message) {
        // 创建Toast元素
        const toast = document.createElement('div');
        toast.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded z-50 transition-opacity';
        toast.textContent = message;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('opacity-100');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('opacity-100');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 1500);
    }
</script>
