using System;
using System.Web;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class favList_del : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string favtypeid = "";

        public string id = "";

        public string backurl = "";

        public string INFO = "";

        public string page = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            backurl = base.Request.QueryString.Get("backurl");
            id = base.Request.QueryString.Get("id");
            page = base.Request.QueryString.Get("page");
            favtypeid = base.Request.QueryString.Get("favtypeid");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            if (!WapTool.IsNumeric(id))
            {
                id = "0";
            }
            IsLogin(userid, backurl);
            switch (action)
            {
                case "godelall":
                    godelall();
                    break;
                case "godel":
                    godel();
                    break;
            }
        }

        public void godel()
        {
            MainBll.UpdateSQL("delete  from favdetail where siteid=" + long.Parse(siteid) + " and userid=" + long.Parse(userid) + " and id=" + long.Parse(id));
            INFO = "OK";
        }

        public void godelall()
        {
            if (favtypeid == "0")
            {
                MainBll.UpdateSQL("delete  from favdetail where siteid=" + long.Parse(siteid) + " and userid=" + long.Parse(userid));
            }
            else
            {
                MainBll.UpdateSQL("delete  from favdetail where siteid=" + long.Parse(siteid) + " and userid=" + long.Parse(userid) + " and favtypeid=" + long.Parse(favtypeid));
            }
            INFO = "OK";
        }
    }
}