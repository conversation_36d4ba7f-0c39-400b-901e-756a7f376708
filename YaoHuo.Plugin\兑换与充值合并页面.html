<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>充值与兑换</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
-webkit-appearance: none;
margin: 0;
}
input[type=number] {
-moz-appearance: textfield;
}
</style>
<script>tailwind.config={theme:{extend:{colors:{primary:'#10B981',secondary:'#4F46E5'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
</head>
<body class="bg-gray-50 text-gray-800">
<!-- 顶部导航栏 -->
<nav class="fixed w-full top-0 bg-white shadow-sm z-10">
<div class="flex items-center justify-between px-4 py-3">
<button class="w-8 h-8 flex items-center justify-center">
<i class="ri-arrow-left-line ri-lg"></i>
</button>
<h1 class="text-xl font-semibold">充值与兑换</h1>
<div class="w-8 h-8"></div>
</div>
</nav>
<!-- 主内容区域 -->
<main class="pt-16 pb-6 px-4">
<!-- 账户信息卡片 -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-4">
<div class="grid grid-cols-2 gap-4">
<div class="text-center">
<div class="text-gray-600 text-sm mb-1">我的 RMB</div>
<div class="text-xl font-medium">¥ 0.00</div>
</div>
<div class="text-center">
<div class="text-gray-600 text-sm mb-1">我的妖晶</div>
<div class="text-xl font-medium">1,609,477</div>
</div>
</div>
</div>
<!-- 切换标签 -->
<div class="bg-gray-100 rounded-full p-1 flex mb-4">
<button id="exchangeTab" class="flex-1 py-2 px-4 rounded-full bg-primary text-white text-center !rounded-button">兑换</button>
<button id="rechargeTab" class="flex-1 py-2 px-4 rounded-full text-gray-700 text-center !rounded-button">充值</button>
</div>
<!-- 充值模块 -->
<div id="rechargeSection" class="bg-white rounded-lg shadow-sm p-4 mb-4">
<h2 class="text-lg font-medium mb-4">扫码付款</h2>
<div class="grid grid-cols-2 gap-4 mb-4">
<button id="alipayBtn" class="flex items-center justify-center gap-2 py-3 px-4 bg-blue-50 text-blue-600 rounded-lg !rounded-button">
<i class="ri-alipay-line ri-lg"></i>
<span>支付宝</span>
</button>
<button id="wechatBtn" class="flex items-center justify-center gap-2 py-3 px-4 bg-gray-100 text-gray-400 rounded-lg !rounded-button">
<i class="ri-wechat-pay-line ri-lg"></i>
<span>微信支付</span>
</button>
</div>
<button id="showQRCodeBtn" class="w-full flex items-center justify-center gap-2 py-3 px-4 bg-primary text-white rounded-lg !rounded-button">
<i class="ri-qr-code-line ri-lg"></i>
<span>显示付款二维码</span>
</button>
<div class="bg-blue-50 rounded-lg p-3 mb-4">
<div class="flex items-start gap-2">
<i class="ri-information-line text-blue-500 mt-0.5"></i>
<div class="flex-1">
<p class="text-blue-800 font-medium">付款时请"添加备注"你的ID</p>
<div class="flex items-center gap-2 mt-1">
<p class="text-blue-700">你的ID号为：<span id="userId" class="text-primary font-semibold">1000</span></p>
<button id="copyIdBtn" class="flex items-center justify-center w-6 h-6 bg-white rounded-full shadow-sm hover:bg-gray-50">
<i class="ri-file-copy-line text-gray-500 ri-sm"></i>
</button>
</div>
</div>
</div>
</div>
<div class="border-t border-gray-100 pt-3">
<p class="text-gray-600 text-sm">付款成功后请「通知站长」收款</p>
<p class="text-gray-600 text-sm mt-1">24小时内会将相应的款值打到你的RMB账户上，可用于购买身份或兑换妖晶。</p>
</div>
</div>
<!-- 兑换模块 -->
<div id="exchangeSection" class="hidden bg-white rounded-lg shadow-sm p-4 mb-4">
<h2 class="text-lg font-medium mb-4">兑换妖晶</h2>
<div class="bg-green-50 rounded-lg p-3 mb-4">
<div class="flex items-center gap-2">
<i class="ri-exchange-line text-green-500"></i>
<p class="text-green-800 font-medium">兑换比例：¥1 元 = 10000 妖晶</p>
</div>
</div>
<div class="mb-4">
<label class="block text-gray-700 text-sm font-medium mb-2">输入金额</label>
<div class="relative">
<span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">¥</span>
<input type="number" id="exchangeAmount" class="w-full pl-8 pr-3 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="请输入RMB数字">
</div>
<div class="mt-2 bg-blue-50 rounded-lg p-3">
<div class="flex items-center gap-2">
<i class="ri-coins-line text-blue-500"></i>
<span class="text-gray-700">可兑换</span>
<span id="crystalAmount" class="text-primary font-medium">0</span>
<span class="text-gray-700">妖晶</span>
</div>
</div>
</div>
<div class="mb-4">
<label class="block text-gray-700 text-sm font-medium mb-2">我的密码</label>
<input type="password" id="password" class="w-full px-3 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="请输入密码确认兑换">
</div>
<button id="exchangeBtn" class="w-full py-3 bg-primary text-white font-medium rounded-lg !rounded-button">确认兑换</button>
</div>
</main>
<!-- 底部标签栏 -->
<!-- 二维码弹窗 -->
<div id="qrCodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20 hidden">
<div class="bg-white rounded-lg w-5/6 max-w-md">
<div class="p-4 border-b border-gray-100 flex justify-between items-center">
<h3 class="text-lg font-medium">付款二维码</h3>
<button id="closeQRCodeBtn" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
<i class="ri-close-line ri-lg text-gray-500"></i>
</button>
</div>
<div class="p-6 flex justify-center">
<div class="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
<i class="ri-qr-code-line ri-5x text-gray-400"></i>
</div>
</div>
</div>
</div>
<!-- 通知站长弹窗 -->
<div id="notifyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20 hidden">
<div class="bg-white rounded-lg w-5/6 max-w-md">
<div class="p-4 border-b border-gray-100">
<h3 class="text-lg font-medium">通知站长</h3>
</div>
<div class="p-4">
<p class="text-gray-600 mb-4">请选择通知方式：</p>
<div class="grid grid-cols-2 gap-3">
<button class="flex items-center justify-center gap-2 py-3 border border-gray-200 rounded-lg !rounded-button">
<i class="ri-wechat-line text-green-500 ri-lg"></i>
<span>微信联系</span>
</button>
<button class="flex items-center justify-center gap-2 py-3 border border-gray-200 rounded-lg !rounded-button">
<i class="ri-qq-line text-blue-500 ri-lg"></i>
<span>QQ联系</span>
</button>
</div>
</div>
<div class="p-4 border-t border-gray-100">
<button id="closeModalBtn" class="w-full py-2 bg-gray-100 text-gray-700 rounded-lg !rounded-button">关闭</button>
</div>
</div>
</div>
<!-- 兑换成功弹窗 -->
<div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20 hidden">
<div class="bg-white rounded-lg w-5/6 max-w-md p-6">
<div class="flex flex-col items-center">
<div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
<i class="ri-check-line ri-2x text-green-500"></i>
</div>
<h3 class="text-xl font-medium mb-2">兑换成功</h3>
<p class="text-gray-600 text-center mb-6">您已成功兑换 <span id="successAmount" class="text-primary font-medium">0</span> 妖晶</p>
<button id="closeSuccessBtn" class="w-full py-3 bg-primary text-white font-medium rounded-lg !rounded-button">确定</button>
</div>
</div>
</div>
<script id="tabSwitchScript">
document.addEventListener('DOMContentLoaded', function() {
const rechargeTab = document.getElementById('rechargeTab');
const exchangeTab = document.getElementById('exchangeTab');
const rechargeSection = document.getElementById('rechargeSection');
const exchangeSection = document.getElementById('exchangeSection');
const alipayBtn = document.getElementById('alipayBtn');
const wechatBtn = document.getElementById('wechatBtn');
rechargeTab.addEventListener('click', function() {
rechargeTab.classList.add('bg-primary', 'text-white');
rechargeTab.classList.remove('text-gray-700');
exchangeTab.classList.remove('bg-primary', 'text-white');
exchangeTab.classList.add('text-gray-700');
rechargeSection.classList.remove('hidden');
exchangeSection.classList.add('hidden');
});
exchangeTab.addEventListener('click', function() {
exchangeTab.classList.add('bg-primary', 'text-white');
exchangeTab.classList.remove('text-gray-700');
rechargeTab.classList.remove('bg-primary', 'text-white');
rechargeTab.classList.add('text-gray-700');
exchangeSection.classList.remove('hidden');
rechargeSection.classList.add('hidden');
});
alipayBtn.addEventListener('click', function() {
alipayBtn.classList.remove('bg-gray-100', 'text-gray-400');
alipayBtn.classList.add('bg-blue-50', 'text-blue-600');
wechatBtn.classList.remove('bg-green-50', 'text-green-600');
wechatBtn.classList.add('bg-gray-100', 'text-gray-400');
});
wechatBtn.addEventListener('click', function() {
wechatBtn.classList.remove('bg-gray-100', 'text-gray-400');
wechatBtn.classList.add('bg-green-50', 'text-green-600');
alipayBtn.classList.remove('bg-blue-50', 'text-blue-600');
alipayBtn.classList.add('bg-gray-100', 'text-gray-400');
});
exchangeTab.click();
});
</script>
<script id="exchangeCalculatorScript">
document.addEventListener('DOMContentLoaded', function() {
const exchangeAmount = document.getElementById('exchangeAmount');
const crystalAmount = document.getElementById('crystalAmount');
exchangeAmount.addEventListener('input', function() {
const amount = parseFloat(this.value) || 0;
const crystals = amount * 10000;
crystalAmount.textContent = crystals.toLocaleString();
});
});
</script>
<script id="modalControlScript">
document.addEventListener('DOMContentLoaded', function() {
const notifyModal = document.getElementById('notifyModal');
const successModal = document.getElementById('successModal');
const qrCodeModal = document.getElementById('qrCodeModal');
const closeModalBtn = document.getElementById('closeModalBtn');
const closeSuccessBtn = document.getElementById('closeSuccessBtn');
const closeQRCodeBtn = document.getElementById('closeQRCodeBtn');
const showQRCodeBtn = document.getElementById('showQRCodeBtn');
const exchangeBtn = document.getElementById('exchangeBtn');
const successAmount = document.getElementById('successAmount');
const crystalAmount = document.getElementById('crystalAmount');
const copyIdBtn = document.getElementById('copyIdBtn');
const userId = document.getElementById('userId');
// 显示二维码弹窗
showQRCodeBtn.addEventListener('click', function() {
qrCodeModal.classList.remove('hidden');
});
// 关闭二维码弹窗
closeQRCodeBtn.addEventListener('click', function() {
qrCodeModal.classList.add('hidden');
});
// 复制ID号
// 添加 Toast 样式
const toastStyle = document.createElement('style');
toastStyle.textContent = `
.toast {
position: fixed;
top: 50%;
left: 50%;
transform: translate(-50%, -50%);
background: rgba(0, 0, 0, 0.8);
color: white;
padding: 8px 16px;
border-radius: 4px;
z-index: 1000;
opacity: 0;
transition: opacity 0.3s ease;
}
.toast.show {
opacity: 1;
}`;
document.head.appendChild(toastStyle);
// 创建 Toast 元素
const toast = document.createElement('div');
toast.className = 'toast';
toast.textContent = '复制成功';
document.body.appendChild(toast);
copyIdBtn.addEventListener('click', function() {
const id = userId.textContent;
navigator.clipboard.writeText(id).then(() => {
copyIdBtn.innerHTML = '<i class="ri-check-line text-green-500 ri-sm"></i>';
toast.classList.add('show');
setTimeout(() => {
toast.classList.remove('show');
}, 1500);
setTimeout(() => {
copyIdBtn.innerHTML = '<i class="ri-file-copy-line text-gray-500 ri-sm"></i>';
}, 1500);
});
});
// 通知站长按钮点击事件
document.querySelectorAll('p:contains("通知站长")').forEach(element => {
element.addEventListener('click', function() {
notifyModal.classList.remove('hidden');
});
});
// 关闭通知站长弹窗
closeModalBtn.addEventListener('click', function() {
notifyModal.classList.add('hidden');
});
// 兑换按钮点击事件
exchangeBtn.addEventListener('click', function() {
const password = document.getElementById('password').value;
if (!password) {
// 显示密码错误提示
const passwordInput = document.getElementById('password');
passwordInput.classList.add('border-red-500');
// 2秒后移除错误样式
setTimeout(() => {
passwordInput.classList.remove('border-red-500');
}, 2000);
return;
}
successAmount.textContent = crystalAmount.textContent;
successModal.classList.remove('hidden');
});
// 关闭兑换成功弹窗
closeSuccessBtn.addEventListener('click', function() {
successModal.classList.add('hidden');
// 重置表单
document.getElementById('exchangeAmount').value = '';
document.getElementById('password').value = '';
crystalAmount.textContent = '0';
});
});
</script>
</body>
</html>