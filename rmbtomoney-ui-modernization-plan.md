# RMBtoMoney 页面 UI 现代化改造 - 项目计划文档

## 📋 项目概况

**目标**：对 `YaoHuo.Plugin` 项目中的 `RMBtoMoney.aspx` 页面进行 UI 现代化改造，使用 Handlebars.NET 替换旧的 ASP.NET Web Forms UI，并增加充值功能卡片，实现"兑换与充值合并页面"的设计。

**相关文件路径**：
- 后端代码：`YaoHuo.Plugin/BBS/RMBtoMoney.aspx.cs`
- 旧版前端：`YaoHuo.Plugin/BBS/RMBtoMoney.aspx`
- 参考设计：`YaoHuo.Plugin/兑换与充值合并页面.html`
- 模板服务：`YaoHuo.Plugin/WebSite/Tool/TemplateService.cs`
- 头部模型：`YaoHuo.Plugin/BBS/Models/HeaderOptionsModel.cs`
- 主布局：`YaoHuo.Plugin/Template/Layouts/MainLayout.hbs`

## 🎯 核心任务分解

### 阶段一：需求分析与设计规划

#### [ ] 任务 1.1：分析现有功能
- [ ] **分析当前 RMBtoMoney 页面功能**：
  - [ ] RMB 转换妖晶功能（已有）
  - [ ] 兑换比例显示（1元 = STATE 妖晶）
  - [ ] 密码验证机制
  - [ ] 各种状态处理（CLOSE、PWERR、NUM、NOTMONEY、MAXMONEY等）
- [ ] **分析参考设计文件**：
  - [ ] 研究 `兑换与充值合并页面.html` 的布局结构
  - [ ] 提取可复用的 UI 组件和交互逻辑
  - [ ] 理解切换标签、二维码弹窗、表单验证等功能

#### [ ] 任务 1.2：确定新功能需求
- [ ] **充值功能卡片设计**：
  - [ ] 扫码付款界面（支付宝/微信支付选择）
  - [ ] 二维码显示弹窗
  - [ ] 用户ID复制功能
  - [ ] 通知站长功能说明
- [ ] **兑换功能优化**：
  - [ ] 实时计算可兑换妖晶数量
  - [ ] 改进表单验证和用户体验
  - [ ] 成功状态的友好提示

### 阶段二：数据模型设计与创建

#### [√] 任务 2.1：创建 RMBtoMoney 页面数据模型
- [√] **文件路径**：`YaoHuo.Plugin/BBS/Models/RMBtoMoneyPageModel.cs`
- [√] **任务详情**：
  - [√] 定义主要页面模型 `RMBtoMoneyPageModel` 类
  - [√] 包含页面标题、消息状态、用户资产、兑换设置等属性
  - [√] 定义子模型：
    - [√] `MessageModel`：处理各种状态消息显示
    - [√] `UserAssetsModel`：用户RMB和妖晶资产信息
    - [√] `ExchangeSettingsModel`：兑换比例和限制设置
    - [√] `RechargeInfoModel`：充值相关信息（用户ID、支付方式等）

#### [√] 任务 2.2：验证数据模型完整性
- [√] **检查项**：
  - [√] 确保所有状态码（OK、CLOSE、PWERR等）的数据需求都被覆盖
  - [√] 验证模型与后端现有变量的映射关系
  - [√] 确认充值功能的占位符字段设计合理

### 阶段三：Handlebars 模板创建

#### [√] 任务 3.1：创建 RMBtoMoney Handlebars 模板
- [√] **文件路径**：`YaoHuo.Plugin/Template/Pages/RMBtoMoney.hbs`
- [√] **任务详情**：
  - [√] **参考设计实现**：
    - [√] 基于 `兑换与充值合并页面.html` 设计整体布局
    - [√] 采用项目现有的 Tailwind CSS 配色和组件风格
    - [√] 参考 `ModifyPassword.hbs` 和 `MyFile.hbs` 的卡片布局模式
  - [√] **模板结构设计**：
    - [√] 账户信息卡片（显示当前RMB和妖晶余额）
    - [√] 切换标签（兑换/充值）
    - [√] 兑换模块（原有功能的现代化实现）
    - [√] 充值模块（新增功能）
    - [√] 各种状态的消息提示
- [√] **功能要求**：
  - [√] 实现标签切换的JavaScript逻辑
  - [√] 添加实时兑换计算功能
  - [√] 实现二维码弹窗和用户ID复制功能
  - [√] 添加表单验证和提交处理

#### [√] 任务 3.2：创建页面专属样式
- [√] **集成到现有CSS系统**：
  - [√] 使用项目现有的 Tailwind CSS 配置
  - [√] 确保与 `MainLayout.hbs` 的样式兼容性
  - [√] 添加必要的自定义样式（如果需要）

### 阶段四：后端代码改造

#### [ ] 任务 4.1：修复 SQL 注入安全问题
- [ ] **文件路径**：`YaoHuo.Plugin/BBS/RMBtoMoney.aspx.cs`
- [ ] **安全问题位置**：第95行 `MainBll.UpdateSQL` 方法
- [ ] **问题代码**：
  ```csharp
  MainBll.UpdateSQL("update [user] set money=money + " + num + ",RMB=RMB-" + tomoney + " where siteid=" + siteid + " and userid=" + userid);
  ```
- [ ] **任务详情**：
  - [ ] 参考 `FriendQueryService.cs` 的参数化查询实现方式
  - [ ] 将字符串拼接改为参数化查询
  - [ ] 使用 `SqlParameter` 确保类型安全
  - [ ] 验证所有输入参数的有效性
  - [ ] 确保修复后功能完全正常

#### [ ] 任务 4.2：实现新旧 UI 切换逻辑
- [ ] **文件路径**：`YaoHuo.Plugin/BBS/RMBtoMoney.aspx.cs`
- [ ] **任务详情**：
  - [ ] 在 `Page_Load` 方法开头添加 UI 偏好检查逻辑
  - [ ] 实现 `CheckAndHandleUIPreference()` 方法
  - [ ] 实现 `TryRenderWithHandlebars()` 方法
  - [ ] 确保正确处理 `ThreadAbortException`
  - [ ] 当新版渲染成功时阻止旧版代码执行

#### [ ] 任务 4.3：实现数据模型构建逻辑
- [ ] **文件路径**：`YaoHuo.Plugin/BBS/RMBtoMoney.aspx.cs`
- [ ] **任务详情**：
  - [ ] 实现 `BuildRMBtoMoneyPageModel()` 私有方法
  - [ ] 处理各种状态消息的数据绑定
  - [ ] 构建用户资产信息（RMB、妖晶余额）
  - [ ] 设置兑换比例和限制参数
  - [ ] 为充值功能设置用户ID等信息（纯前端展示）

#### [ ] 任务 4.4：实现 Handlebars 渲染调用
- [ ] **文件路径**：`YaoHuo.Plugin/BBS/RMBtoMoney.aspx.cs`
- [ ] **任务详情**：
  - [ ] 实现 `RenderWithHandlebars()` 方法
  - [ ] 调用 `TemplateService.RenderPageWithLayout` 方法
  - [ ] 配置正确的模板路径：`~/Template/Pages/RMBtoMoney.hbs`
  - [ ] 设置页面标题为"充值与兑换"
  - [ ] 配置适当的 `HeaderOptionsModel`（使用项目自有header，非白色header）
  - [ ] 处理渲染错误和异常情况

### 阶段五：前端功能实现

#### [ ] 任务 5.1：实现标签切换功能
- [ ] **位置**：`RMBtoMoney.hbs` 模板中的 JavaScript 代码
- [ ] **任务详情**：
  - [ ] 实现兑换/充值标签的切换逻辑
  - [ ] 控制对应内容区域的显示/隐藏
  - [ ] 确保标签状态的视觉反馈

#### [ ] 任务 5.2：实现兑换计算功能
- [ ] **位置**：`RMBtoMoney.hbs` 模板中的 JavaScript 代码
- [ ] **任务详情**：
  - [ ] 监听金额输入框的变化
  - [ ] 实时计算可兑换的妖晶数量
  - [ ] 显示计算结果并格式化数字

#### [ ] 任务 5.3：实现充值相关功能
- [ ] **位置**：`RMBtoMoney.hbs` 模板中的 JavaScript 代码
- [ ] **任务详情**：
  - [ ] 支付方式选择（支付宝/微信支付）
  - [ ] 二维码弹窗的显示和关闭
  - [ ] 用户ID复制到剪贴板功能
  - [ ] Toast 提示消息

#### [ ] 任务 5.4：实现表单验证和提交
- [ ] **位置**：`RMBtoMoney.hbs` 模板中的 JavaScript 代码
- [ ] **任务详情**：
  - [ ] 兑换表单的前端验证
  - [ ] 密码输入验证
  - [ ] 提交状态的视觉反馈
  - [ ] 成功/失败状态的处理

### 阶段六：测试与验证

#### [ ] 任务 6.1：功能测试
- [ ] **测试项目**：
  - [ ] 测试新旧 UI 切换功能
  - [ ] 测试兑换功能的各种状态处理
  - [ ] 测试充值界面的交互功能
  - [ ] 测试标签切换和计算功能
  - [ ] 测试二维码弹窗和复制功能
  - [ ] 测试表单验证和提交流程

#### [ ] 任务 6.2：样式测试
- [ ] **测试项目**：
  - [ ] 检查新版 UI 是否有样式问题
  - [ ] 测试响应式设计在不同设备上的显示效果
  - [ ] 验证与现有页面风格的一致性
  - [ ] 确认所有交互元素的视觉反馈

#### [ ] 任务 6.3：错误处理测试
- [ ] **测试项目**：
  - [ ] 测试各种错误状态的显示
  - [ ] 测试模板渲染失败时的回退机制
  - [ ] 验证JavaScript错误的处理

### 阶段七：代码质量与优化

#### [ ] 任务 7.1：代码审查
- [ ] **审查项目**：
  - [ ] 确保代码符合项目的 C# 编码规范
  - [ ] 验证命名约定的一致性
  - [ ] 检查注释和文档的完整性
  - [ ] 确认错误处理的完备性

#### [ ] 任务 7.2：性能优化
- [ ] **优化项目**：
  - [ ] 确认模板缓存机制工作正常
  - [ ] 检查数据模型构建的效率
  - [ ] 验证前端 JavaScript 的性能

#### [ ] 任务 7.3：项目文件管理
- [ ] **文件操作**：
  - [ ] 确保新创建的文件已添加到 `YaoHuo.Plugin.csproj` 项目中
  - [ ] 验证文件的"生成操作"设置正确
  - [ ] 检查命名空间引用的正确性

## 🎯 成功标准检查清单

- [ ] RMBtoMoney 页面能够成功通过 Handlebars 渲染出新的 UI
- [ ] 新 UI 包含兑换和充值两个功能模块
- [ ] 兑换功能保持原有的所有业务逻辑和状态处理
- [ ] 充值功能提供完整的用户指引和交互体验
- [ ] 标签切换、实时计算、二维码弹窗等前端功能正常工作
- [ ] 新旧 UI 切换功能工作正常
- [ ] 没有明显的 UI 错误或样式问题
- [ ] 页面风格与项目整体设计保持一致
- [ ] 所有交互功能提供良好的用户体验

## 📝 注意事项

1. **安全优先**：必须修复第95行的SQL注入问题，使用参数化查询确保安全
2. **功能完整性**：确保原有的兑换功能完全保留，不影响现有业务逻辑
3. **设计一致性**：新版 UI 应与项目现有页面（如 MyFile、ModifyPassword）保持风格一致
4. **Header样式**：使用项目自有的header设计，参考HTML文件布局但不采用白色header
5. **充值功能定位**：充值卡片为纯前端介绍性功能，不需要后端aspx.cs参与处理
6. **用户体验**：充值功能作为新增功能，需要提供清晰的操作指引和交互反馈
7. **兼容性保证**：确保新旧 UI 切换机制的稳定性
8. **响应式设计**：确保在不同设备上都有良好的显示效果

## 🔄 依赖关系

- 任务 1（需求分析）是所有后续任务的前置条件
- 任务 2（数据模型）是任务 3、4 的前置条件
- 任务 3（模板创建）可与任务 4（后端改造）并行进行
- 任务 5（前端功能）依赖任务 3 的完成
- 任务 6（测试）依赖前面所有开发任务的完成

---

## 📚 详细实现指导

### SQL注入修复示例

**当前有问题的代码（第95行）**：
```csharp
MainBll.UpdateSQL("update [user] set money=money + " + num + ",RMB=RMB-" + tomoney + " where siteid=" + siteid + " and userid=" + userid);
```

**修复后的安全代码**：
```csharp
// 参考 FriendQueryService 的实现方式
string sql = "UPDATE [user] SET money = money + @addMoney, RMB = RMB - @deductRMB WHERE siteid = @siteid AND userid = @userid";
var parameters = new SqlParameter[]
{
    new SqlParameter("@addMoney", SqlDbType.BigInt) { Value = num },
    new SqlParameter("@deductRMB", SqlDbType.Decimal) { Value = decimal.Parse(tomoney) },
    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
    new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
};
MainBll.ExecuteSQL(sql, parameters);
```

### 数据模型结构示例

```csharp
public class RMBtoMoneyPageModel
{
    public string PageTitle { get; set; } = "充值与兑换";
    public MessageModel Message { get; set; } = new MessageModel();
    public UserAssetsModel UserAssets { get; set; } = new UserAssetsModel();
    public ExchangeSettingsModel ExchangeSettings { get; set; } = new ExchangeSettingsModel();
    public RechargeInfoModel RechargeInfo { get; set; } = new RechargeInfoModel();
    public FormDataModel FormData { get; set; } = new FormDataModel();
}

public class UserAssetsModel
{
    public decimal CurrentRMB { get; set; }
    public long CurrentMoney { get; set; }
    public string MoneyName { get; set; }
    public string RMBDisplay => $"¥{CurrentRMB:F2}";
    public string MoneyDisplay => CurrentMoney.ToString("N0");
}

public class ExchangeSettingsModel
{
    public long ExchangeRate { get; set; } // 1元 = ExchangeRate 妖晶
    public decimal MaxExchangeAmount { get; set; } = 1000.00m;
    public bool IsEnabled { get; set; } = true;
    public string RateDisplay => $"¥1 元 = {ExchangeRate} 妖晶";
}

public class RechargeInfoModel
{
    public string UserId { get; set; }
    public bool ShowQRCode { get; set; } = false;
    public string PaymentMethod { get; set; } = "alipay"; // alipay, wechat
}
```

### 模板结构示例

```handlebars
{{! 状态消息显示 }}
{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/if}}

{{! 账户信息卡片 }}
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="wallet" class="card-icon"></i>
            我的资产
        </h2>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-gray-600 text-sm mb-1">我的 RMB</div>
                <div class="text-xl font-medium">{{UserAssets.RMBDisplay}}</div>
            </div>
            <div class="text-center">
                <div class="text-gray-600 text-sm mb-1">我的{{UserAssets.MoneyName}}</div>
                <div class="text-xl font-medium">{{UserAssets.MoneyDisplay}}</div>
            </div>
        </div>
    </div>
</div>

{{! 切换标签 }}
<div class="bg-gray-100 rounded-full p-1 flex mb-4">
    <button id="exchangeTab" class="flex-1 py-2 px-4 rounded-full bg-primary text-white text-center">兑换</button>
    <button id="rechargeTab" class="flex-1 py-2 px-4 rounded-full text-gray-700 text-center">充值</button>
</div>

{{! 兑换模块 }}
<div id="exchangeSection" class="card">
    <!-- 兑换表单内容 -->
</div>

{{! 充值模块 }}
<div id="rechargeSection" class="card hidden">
    <!-- 充值介绍内容 -->
</div>
```

---

**创建时间**：2024年12月19日
**项目版本**：基于 .NET Framework 4.8 + Handlebars.NET
**参考文档**：`handlebars-integration-reference.md`、`friendlist-ui-modernization-todo.md`
